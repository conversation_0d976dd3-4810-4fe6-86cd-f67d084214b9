<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <Button
        android:id="@+id/btnReload"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_gravity="bottom"
        android:text="reload"/>

    <LinearLayout
        android:id="@+id/ll_root"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/activity_bg"
        android:orientation="vertical">

        <include layout="@layout/common_header_items_cart_close" />

        <FrameLayout
            android:id="@+id/fl_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <TextView
            android:id="@+id/tv_error"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:background="@color/activity_bg"
            android:gravity="center"
            android:lineSpacingExtra="1dp"
            android:lineSpacingMultiplier="1.3"
            android:text="哎呀,网络不给力\n点击重新加载"
            android:textSize="16sp"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/rl_network_tip"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableTop="@drawable/icon_no_network"
                android:drawablePadding="15dp"
                android:gravity="center"
                android:text="暂无网络"
                android:textColor="#FF9494A6"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_fresh"
                android:layout_width="160dp"
                android:layout_height="44dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/bg_rect_radio_green_shape"
                android:gravity="center"
                android:text="刷新一下"
                android:textColor="@color/white"
                android:textSize="16sp" />
        </LinearLayout>

    </LinearLayout>

    <ImageView
        android:id="@+id/iv_ad_suspension"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_gravity="bottom|right"
        android:layout_marginBottom="110dp"
        android:src="@drawable/icon_ad_suspension"
        android:visibility="invisible" />

</FrameLayout>