<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mRootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/include"
        layout="@layout/common_header_items"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/mSubmitTv"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginStart="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="20dp"
        android:gravity="center"
        android:text="@string/submit"
        android:textColor="@color/white"
        android:textSize="17sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:rv_backgroundColor="@color/color_00B377"
        app:rv_cornerRadius="35dp" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nestedScrollView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="20dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toTopOf="@+id/mSubmitTv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/include">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_10"
                android:background="@color/invoice_tv_F7F7F8" />

            <TextView
                android:id="@+id/mCompanyNameTv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="10dp"
                android:paddingTop="10dp"
                android:paddingRight="10dp"
                android:paddingBottom="10dp"
                android:textColor="@color/text_676773"
                android:textSize="16sp"
                tools:text="广州医药集团有限公司" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginLeft="10dp"
                android:background="@color/colors_f5f5f5" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="20dp">

                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="收货人："
                    android:textColor="@color/text_9494A6"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/mReceiverTv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_676773"
                    android:textSize="15sp"
                    tools:text="王可可" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="20dp">

                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="联系电话："
                    android:textColor="@color/text_9494A6"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/mPhoneTv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_676773"
                    android:textSize="15sp"
                    tools:text="15201622912" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="20dp">

                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="收货地址："
                    android:textColor="@color/text_9494A6"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/mAddressTv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_676773"
                    android:textSize="15sp"
                    tools:text="湖北省武汉市东湖区金山大道（湖北省武汉市东西湖区沿海赛洛城)" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:layout_marginBottom="10dp"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="20dp">

                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="备注："
                    android:textColor="@color/text_9494A6"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/mRemarksTv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_676773"
                    android:textSize="15sp"
                    tools:text="拒绝到付" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_10"
                android:background="@color/invoice_tv_F7F7F8" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/textView10"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingLeft="10dp"
                    android:paddingTop="10dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="10dp"
                    android:text="请选择物流方式："
                    android:textColor="@color/text_676773"
                    android:textSize="15sp"
                    app:layout_constraintEnd_toStartOf="@+id/guideline"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.40389293" />

                <TextView
                    android:id="@+id/mMailWayTv"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:gravity="center_vertical"
                    android:textColor="@color/text_676773"
                    android:textSize="15sp"
                    app:layout_constraintBottom_toBottomOf="@+id/textView10"
                    app:layout_constraintEnd_toStartOf="@+id/mMailWayArrow"
                    app:layout_constraintStart_toStartOf="@+id/guideline"
                    app:layout_constraintTop_toTopOf="@+id/textView10"
                    tools:text="顺丰快递" />

                <ImageView
                    android:id="@+id/mMailWayArrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:layout_marginRight="10dp"
                    android:src="@drawable/ic_arrow"
                    app:layout_constraintBottom_toBottomOf="@+id/mMailWayTv"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/mMailWayTv" />

                <View
                    android:id="@+id/view"
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginLeft="10dp"
                    android:background="@color/colors_f5f5f5"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView10" />

                <TextView
                    android:id="@+id/textView11"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingLeft="10dp"
                    android:paddingTop="10dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="10dp"
                    android:text="请填写物流运单号："
                    android:textColor="@color/text_676773"
                    android:textSize="15sp"
                    app:layout_constraintEnd_toStartOf="@+id/guideline"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/view" />

                <EditText
                    android:id="@+id/mCodeEt"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@null"
                    android:digits="@string/inputtype_digits"
                    android:gravity="center_vertical"
                    android:hint="请输入"
                    android:inputType="textVisiblePassword"
                    android:maxLength="20"
                    android:textColor="@color/text_676773"
                    android:textColorHint="@color/text_9494A6"
                    android:textSize="15sp"
                    app:layout_constraintBottom_toBottomOf="@+id/textView11"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/textView11"
                    app:layout_constraintTop_toTopOf="@+id/textView11" />

                <View
                    android:id="@+id/textView12"
                    android:layout_width="0dp"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginLeft="10dp"
                    android:background="@color/colors_f5f5f5"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView11" />

                <TextView
                    android:id="@+id/textView13"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingLeft="10dp"
                    android:paddingTop="10dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="10dp"
                    android:text="请上传邮寄图片："
                    android:textColor="@color/text_676773"
                    android:textSize="15sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView12" />


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="90dp"
                    android:layout_height="90dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView13">

                    <com.ybmmarket20.common.widget.RoundedImageView
                        android:id="@+id/mPicIv"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_marginStart="8dp"
                        android:layout_marginLeft="8dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="8dp"
                        android:layout_marginRight="8dp"
                        android:layout_marginBottom="8dp"
                        android:src="@drawable/ic_add_pic"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="1.0" />

                    <ImageView
                        android:id="@+id/mDeletePicIv"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:src="@drawable/ic_delete_pic"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>