package com.ybmmarket20.activity;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Rect;
import android.os.Bundle;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.RelativeLayout;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CheckOrderListBean;
import com.ybmmarket20.bean.CheckOrderRowsBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;
import butterknife.ButterKnife;
import butterknife.OnClick;

/*
 * 选择申请订单
 * */
@Router({"selectapplyfororder"})
public class SelectApplyForOrderActivity extends BaseActivity {

    @Bind(R.id.rl_protect_price)
    RelativeLayout rlProtectPrice;
    @Bind(R.id.list)
    CommonRecyclerView list;

    private SelectApplyForOrderAdapter adapter;
    private List<CheckOrderRowsBean> rows = new ArrayList<>();
    private int bottom = com.ybm.app.utils.UiUtils.dp2px(6);
    private SimpleDateFormat dateFormat;
    private int pageSize = 10;
    private int pager = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initReceiver();
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_select_apply_for_order;
    }

    @Override
    protected void initData() {

        setTitle("选择申请订单");
        setRigthImg(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                RoutersUtils.open("ybmpage://selectapplyforordersearch");

            }
        }, R.drawable.icon_protect_price_search);
        adapter = new SelectApplyForOrderAdapter(R.layout.item_select_apply_for_order, rows);
        list.setListener(new CommonRecyclerView.Listener() {
            @Override
            public void onRefresh() {
                getData(pager = 0);
            }

            @Override
            public void onLoadMore() {
                getData(pager);
            }
        });
        list.setEnabled(false);
        list.setAdapter(adapter);
        list.setEmptyView(R.layout.layout_empty_view, R.drawable.icon_empty, "暂无可申请订单");
        adapter.openLoadMore(10, true);
        list.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                outRect.bottom = bottom;
            }
        });

    }

    /*
     * 获取保驾护航申请记录数据
     * */
    public void getData(final int page) {
        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        params.put("limit", String.valueOf(pageSize));
        params.put("offset", String.valueOf(String.valueOf(page)));
        HttpManager.getInstance().post(AppNetConfig.FIND_ORDERS_ESCORT, params, new BaseResponse<CheckOrderListBean>() {

            @Override
            public void onSuccess(String content, BaseBean<CheckOrderListBean> brandBean, CheckOrderListBean rowsBeans) {
                completion();
                if (brandBean != null) {
                    if (brandBean.isSuccess()) {

                        if (rowsBeans != null) {

                            if (rowsBeans.getRows() != null && rowsBeans.getRows().size() > 0) {
                                if (page <= 0) {
                                    SelectApplyForOrderActivity.this.pager = 1;
                                } else {
                                    SelectApplyForOrderActivity.this.pager++;
                                }
                            }
                            if (page <= 0) {

                                if (rows == null) {
                                    rows = new ArrayList<>();
                                }

                                if (rows.size() <= 0 && rowsBeans.getRows() != null) {
                                    rows.addAll(rowsBeans.getRows());
                                } else {
                                    if (rowsBeans.getRows() == null || rowsBeans.getRows().isEmpty()) {

                                    } else {

                                        rows.addAll(0, rowsBeans.getRows());
                                    }
                                }
                                adapter.setNewData(rows);
                                adapter.notifyDataChangedAfterLoadMore(rows.size() >= pageSize);
                            } else {
                                if (rowsBeans.getRows() == null || rowsBeans.getRows().size() <= 0) {
                                    adapter.notifyDataChangedAfterLoadMore(false);
                                } else {
                                    rows.addAll(rowsBeans.getRows());
                                    adapter.setNewData(rows);
                                    adapter.notifyDataChangedAfterLoadMore(rowsBeans.getRows().size() >= pageSize);
                                }
                            }
                        }
                    } else {
                        adapter.setNewData(rows);
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                completion();
                if (list != null) {
                    list.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                if (list != null) {
                                    adapter.setNewData(rows);
                                }
                            } catch (Throwable e) {
                                BugUtil.sendBug(e);
                            }
                        }
                    }, 300);
                }
            }
        });

    }

    private void completion() {
        if (list != null) {
            try {
                list.setRefreshing(false);
            } catch (Throwable e) {
                BugUtil.sendBug(e);
            }
        }
    }

    @OnClick({R.id.rl_protect_price})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.rl_protect_price:
                RoutersUtils.open("ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/temp/baojiahuhangrule.html");
                break;
        }
    }

    private void initReceiver() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(IntentCanst.FINISH_THE_ESCORT);
        LocalBroadcastManager.getInstance(this.getApplicationContext()).registerReceiver(mRefreshBroadcastReceiver, intentFilter);
    }

    /*
     * 广播
     * */
    private BroadcastReceiver mRefreshBroadcastReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (IntentCanst.FINISH_THE_ESCORT.equals(action)) {
                finish();
            }
        }
    };

    @Override
    protected void onDestroy() {
        super.onDestroy();
        ButterKnife.unbind(this);
        if (mRefreshBroadcastReceiver != null) {
            LocalBroadcastManager.getInstance(this.getApplicationContext()).unregisterReceiver(mRefreshBroadcastReceiver);
        }
    }
}
