package com.ybmmarket20.home

import android.content.Context
import android.os.Handler
import android.os.Looper
import com.ybmmarket20.bean.HomeConfigBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.home.newpage.HomeSteadyLayoutFragmentV3
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.page.home.HomeReportConstant
import com.ybmmarket20.xyyreport.page.home.HomeReportEvent
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil
import com.ybmmarket20.xyyreport.spm.TrackData
import com.ybmmarket20.xyyreport.spm.XyyReportActivity

abstract class MainAnalysisActivity: BaseActivity() {

    var mBottomTabTrackData: TrackData? = null
    var mToTopBottomTabTrackData: TrackData? = null

    fun generateOrderTrackData(): TrackData {
        val spm = getSpmCtn()?.newInstance()?.apply {
            spmC = "newIndexTabbar@Z"
            spmD = "icon@x"
        }
        val scm = ScmBean(
            "cms",
            "0",
            "0_0",
            "link-orderList_text-订单",
            ""
        )
        return TrackData(spm, scm)
    }

    fun generateFrequentlyBuyTrackData(): TrackData {
        val spm = getSpmCtn()?.newInstance()?.apply {
            spmC = "newIndexTabbar@Z"
            spmD = "icon@y"
        }
        val scm = ScmBean(
            "cms",
            "0",
            "0_0",
            "link-oftenBuySearch_text-常购常搜",
            ""
        )
        return TrackData(spm, scm)
    }

    /**
     * 处理所有tab的spmD的位置
     */
    fun handleTabTrackData(configBean: HomeConfigBean?) {
        listOf(
            configBean?.bottom_first_button_track_data,
            configBean?.bottom_frequently_button_track_data,
//            configBean?.bottom_second_button_track_data,
            configBean?.bottom_third_button_track_data,
            configBean?.bottom_fourth_button_track_data,
            configBean?.bottom_fifth_button_track_data
        ).forEachIndexed { index, trackData ->
            try {
                val preSpmD = trackData?.spmEntity?.spmD?.takeIf { it.contains("@") }?.split("@")?.get(0)
                trackData?.spmEntity?.spmD = "$preSpmD@${index+1}"
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    fun handleSelectTabPvData(selectedIndex: Int, curPosition: Int) {
        if (selectedIndex == curPosition) return
        if (selectedIndex == 0) {
            //首页
            if (getHomeFragment() is HomeSteadyLayoutFragmentV3) {
                (getHomeFragment() as HomeSteadyLayoutFragmentV3).onHandleFragmentPv()
            }
        } else {
            setSpmCtn(null)
        }
    }

    fun onBottomTabClick(vTag: Any?, selectedPosition: Int, curPosition: Int) {
        if (vTag !is TrackData || selectedPosition == curPosition || getSpmCtn() == null) return
        SpmLogUtil.print("首页-组件-底部tab点击")
        vTag.spmEntity?.spmB = getSpmCtn()?.spmB
        vTag.scmEntity?.scmC = getScmCnt()?.scmC
        HomeReportEvent.trackHomeSubComponentClick(this, vTag.spmEntity, vTag.scmEntity)
    }

    fun onBottomTabDefaultClick(trackData: TrackData?) {
        if (getSpmCtn() == null) return
        mToTopBottomTabTrackData = trackData
        SpmLogUtil.print("首页-组件-底部tab默认点击-(该事件不上报)")
        trackData?.spmEntity?.spmB = getSpmCtn()?.spmB
//        HomeReportEvent.trackHomeSubComponentClick(this, trackData?.spmEntity, trackData?.scmEntity)
    }

    fun onBottomTabTopClick() {
        if (getSpmCtn() == null) return
        SpmLogUtil.print("首页-组件-底部tab回到顶部点击")
        val trackData = mToTopBottomTabTrackData?.newTrackData()
        trackData?.apply {
            spmEntity?.spmB = getSpmCtn()?.spmB
            scmEntity?.scmD = "link-newhome_text-首页TOP"
        }
        HomeReportEvent.trackHomeSubComponentClick(this, trackData?.spmEntity, trackData?.scmEntity)
    }

    fun trackTabComponentExposure(context: Context, trackData: TrackData?) {
        if (context is XyyReportActivity) {
            val isHomeSwitchUser = context.getExtensionValue(HomeReportConstant.EXTENSION_HOME_REPORT_IS_SWITCH_USER)
            if (isHomeSwitchUser != null && isHomeSwitchUser as Boolean){
                context.putExtension(HomeReportConstant.EXTENSION_HOME_REPORT_IS_SWITCH_USER, false)
                return
            }
        }
        if (trackData != null) {
            mBottomTabTrackData = trackData
        }
//        if (trackData?.spmEntity?.concat() == mBottomTabTrackData?.spmEntity?.concat()) return
        if (mBottomTabTrackData == null) return
        SpmLogUtil.print("首页-组件-底部tab曝光")
        if (context is XyyReportActivity) {
            mBottomTabTrackData?.spmEntity?.spmB = context.getSpmCtn()?.spmB
        }
        HomeReportEvent.trackHomeComponentExposure(context, mBottomTabTrackData?.spmEntity)
    }

    fun trackCommonTabComponentExposure(context: Context) {
        SpmUtil.checkAnalysisContext(context) { reportActivity->
            val tabTrackData = mBottomTabTrackData?.newTrackData()?.apply {
                spmEntity?.newInstance()?.apply {
                    this.spmB = reportActivity.getSpmCtn()?.spmB
                }
            }
            trackTabComponentExposure(context, tabTrackData)
        }
    }


    fun trackClickTabComponentExposure() {
        trackTabComponentExposure(this, null)
    }

    abstract fun getHomeFragment(): IHomeSteadyFragment?
}